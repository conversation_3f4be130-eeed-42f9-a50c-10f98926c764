#!/usr/bin/env node

// Test script to verify our AP3X logo changes
import { UI } from './packages/opencode/src/cli/ui.ts';

console.log('\n🎯 Testing AP3X Logo Integration\n');
console.log('='.repeat(50));
console.log('OpenCode Logo (should show AP3X):');
console.log('='.repeat(50));
console.log(UI.logo());
console.log('='.repeat(50));
console.log('\n✅ Logo test complete!\n');
console.log('🔸 AP3 should be in WHITE');
console.log('🔸 X should be in RED');
console.log('🔸 Filled block style\n');
