<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CYBERPUNK CALENDAR</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Share+Tech+Mono&display=swap" rel="stylesheet">
</head>
<body>
    <div class="cyber-container">
        <div class="grid-overlay"></div>
        
        <header class="cyber-header">
            <div class="header-glow"></div>
            <h1 class="cyber-title">
                <span class="glitch-text" data-text="CYBER-CAL">CYBER-CAL</span>
            </h1>
            <div class="system-status">
                <span class="status-indicator active"></span>
                <span class="status-text">SYSTEM ONLINE</span>
            </div>
        </header>

        <main class="calendar-container">
            <div class="calendar-header">
                <button class="nav-btn prev-btn" id="prevBtn">
                    <span class="btn-text">&lt; PREV</span>
                </button>
                <div class="month-display">
                    <h2 id="currentMonth" class="month-text"></h2>
                    <div class="year-display" id="currentYear"></div>
                </div>
                <button class="nav-btn next-btn" id="nextBtn">
                    <span class="btn-text">NEXT &gt;</span>
                </button>
            </div>

            <div class="calendar-grid">
                <div class="weekday-headers">
                    <div class="weekday">SUN</div>
                    <div class="weekday">MON</div>
                    <div class="weekday">TUE</div>
                    <div class="weekday">WED</div>
                    <div class="weekday">THU</div>
                    <div class="weekday">FRI</div>
                    <div class="weekday">SAT</div>
                </div>
                <div class="calendar-days" id="calendarDays"></div>
            </div>

            <div class="event-panel">
                <div class="panel-header">
                    <h3>EVENT LOG</h3>
                    <button class="add-event-btn" id="addEventBtn">+ NEW EVENT</button>
                </div>
                <div class="events-list" id="eventsList">
                    <div class="no-events">No events scheduled</div>
                </div>
            </div>
        </main>

        <div class="event-modal" id="eventModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>CREATE EVENT</h3>
                    <button class="close-btn" id="closeModal">&times;</button>
                </div>
                <form id="eventForm">
                    <div class="form-group">
                        <label>EVENT TITLE</label>
                        <input type="text" id="eventTitle" required>
                    </div>
                    <div class="form-group">
                        <label>DATE</label>
                        <input type="date" id="eventDate" required>
                    </div>
                    <div class="form-group">
                        <label>DESCRIPTION</label>
                        <textarea id="eventDesc"></textarea>
                    </div>
                    <div class="form-group">
                        <label>PRIORITY</label>
                        <select id="eventPriority">
                            <option value="low">LOW</option>
                            <option value="medium">MEDIUM</option>
                            <option value="high">HIGH</option>
                        </select>
                    </div>
                    <button type="submit" class="submit-btn">CREATE EVENT</button>
                </form>
            </div>
        </div>

        <div class="scan-line"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>