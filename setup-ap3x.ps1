# Setup script to make AP3X globally accessible
# Run this script as Administrator to add AP3X to your system PATH

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   AP3X Global Setup Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Get the current directory (where AP3X.bat is located)
$currentDir = Get-Location
$ap3xPath = Join-Path $currentDir "AP3X.bat"

# Check if AP3X.bat exists
if (-not (Test-Path $ap3xPath)) {
    Write-Host "ERROR: AP3X.bat not found in current directory!" -ForegroundColor Red
    Write-Host "Please run this script from the opencode-dev directory." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found AP3X.bat at: $ap3xPath" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "WARNING: Not running as Administrator" -ForegroundColor Yellow
    Write-Host "This script will add the path to your USER environment variables only." -ForegroundColor Yellow
    Write-Host "For system-wide access, please run as Administrator." -ForegroundColor Yellow
    Write-Host ""
}

# Get current PATH
if ($isAdmin) {
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    $scope = "Machine"
    Write-Host "Adding to SYSTEM PATH..." -ForegroundColor Yellow
} else {
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    $scope = "User"
    Write-Host "Adding to USER PATH..." -ForegroundColor Yellow
}

# Check if the directory is already in PATH
if ($currentPath -split ";" | Where-Object { $_ -eq $currentDir }) {
    Write-Host "Directory is already in PATH!" -ForegroundColor Green
} else {
    # Add the directory to PATH
    $newPath = $currentPath + ";" + $currentDir
    [Environment]::SetEnvironmentVariable("PATH", $newPath, $scope)
    Write-Host "Successfully added $currentDir to PATH!" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Setup Complete!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "You can now type 'AP3X' from any terminal to launch OpenCode!" -ForegroundColor Green
Write-Host ""
Write-Host "Note: You may need to restart your terminal or command prompt" -ForegroundColor Yellow
Write-Host "for the PATH changes to take effect." -ForegroundColor Yellow
Write-Host ""
Write-Host "Test it by opening a new terminal and typing: AP3X" -ForegroundColor Cyan
Write-Host ""

Read-Host "Press Enter to exit"
