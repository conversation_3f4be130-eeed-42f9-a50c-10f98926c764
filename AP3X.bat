@echo off
setlocal enabledelayedexpansion

REM AP3X - Quick launcher for OpenCode development server
REM This script allows you to launch OpenCode by typing "AP3X" from anywhere

echo.
echo ========================================
echo   AP3X - OpenCode Launcher
echo ========================================
echo.

REM Store current directory to restore later
set "ORIGINAL_DIR=%CD%"

REM Change to the OpenCode project directory
set "PROJECT_DIR=C:\Users\<USER>\Downloads\opencode-dev"
cd /d "%PROJECT_DIR%"

REM Check if we're in the right directory
if not exist "package.json" (
    echo ERROR: Could not find OpenCode project at %PROJECT_DIR%
    echo Please update the PROJECT_DIR variable in this script.
    pause
    exit /b 1
)

REM Check if Bun is available
echo [1/3] Checking Bun...
bun --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Bun not found! Please ensure Bun is installed and in your PATH.
    echo You can install Bun from: https://bun.sh/
    pause
    exit /b 1
)
echo ✓ Bun is available

REM Check if dependencies are installed
echo [2/3] Checking dependencies...
if not exist "node_modules" (
    echo Installing dependencies...
    bun install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)
echo ✓ Dependencies ready

REM Start the development server
echo [3/3] Starting OpenCode development server...
echo.
echo ----------------------------------------
echo  🚀 AP3X is launching OpenCode...
echo  Press Ctrl+C to stop the server
echo ----------------------------------------
echo.

REM Run the development server
bun run dev

REM If we get here, the server has stopped
echo.
echo ----------------------------------------
echo  AP3X session ended
echo ----------------------------------------
echo.

REM Restore original directory
cd /d "%ORIGINAL_DIR%"

REM Keep window open if there was an error
if %errorlevel% neq 0 (
    echo An error occurred. Press any key to exit...
    pause >nul
)
