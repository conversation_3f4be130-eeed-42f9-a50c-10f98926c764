// Simple test to show our AP3X logo
console.log('\n🎯 AP3X Logo Test\n');
console.log('='.repeat(50));

// Simulate the logo output with colors
const WHITE = '\x1b[97m\x1b[1m';
const RED = '\x1b[91m\x1b[1m';
const RESET = '\x1b[0m';

const LOGO = [
  [` █████╗ ██████╗ ██████╗`, ` ██╗  ██╗`],
  [`██╔══██╗██╔══██╗╚════██╗`, `╚██╗██╔╝`],
  [`███████║██████╔╝ █████╔╝`, ` ╚███╔╝ `],
  [`██╔══██║██╔═══╝  ╚═══██╗`, ` ██╔██╗ `],
  [`██║  ██║██║     ██████╔╝`, `██╔╝ ██╗`],
  [`╚═╝  ╚═╝╚═╝     ╚═════╝ `, `╚═╝  ╚═╝`],
];

for (const row of LOGO) {
  console.log(WHITE + row[0] + RESET + RED + row[1] + RESET);
}

console.log('\n' + '='.repeat(50));
console.log('✅ AP3X Logo Integration Complete!');
console.log('🔸 AP3 = White');
console.log('🔸 X = Red');
console.log('🔸 This logo now appears in OpenCode!\n');
