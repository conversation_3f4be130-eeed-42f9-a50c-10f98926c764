{"$schema": "https://opencode.ai/config.json", "model": "openrouter/moonshotai/kimi-k2", "provider": {"openrouter": {"options": {"apiKey": "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"}, "models": {"moonshotai/kimi-k2": {"options": {"provider": {"order": ["baseten"], "allow_fallbacks": false}}}}}, "huggingface": {"models": {"Qwen/Qwen3-235B-A22B-Instruct-2507:fireworks-ai": {}}}}, "mcp": {"context7": {"type": "remote", "url": "https://mcp.context7.com/sse"}, "weather": {"type": "local", "command": ["opencode", "x", "@h1deya/mcp-server-weather"]}}}